<template>
  <div class="user-management">
    <n-card>
      <!-- 操作栏 -->
      <div class="action-bar">
        <n-space>
          <!-- 过滤器 -->
          <n-button @click="showFilterModal = true" :type="filters.length > 0 ? 'primary' : 'default'">
            <template #icon>
              <n-icon><FilterIcon /></n-icon>
            </template>
            过滤器 {{ filters.length > 0 ? `(${filters.length})` : '' }}
          </n-button>

          <!-- 批量操作 -->
          <n-dropdown :options="batchOptions" @select="handleBatchAction">
            <n-button>
              <template #icon>
                <n-icon><MoreIcon /></n-icon>
              </template>
              操作
            </n-button>
          </n-dropdown>

          <!-- 添加用户按钮 -->
          <n-button type="primary" @click="openCreateModal" :loading="loading">
            <template #icon>
              <n-icon><AddIcon /></n-icon>
            </template>
            添加用户
          </n-button>
        </n-space>
      </div>

      <!-- 用户表格 -->
      <n-data-table
        :columns="columns"
        :data="users"
        :loading="loading"
        :pagination="paginationConfig"
        :remote="true"
        :scroll-x="1800"
        @update:sorter="handleSorterChange"
      />
    </n-card>

    <!-- 创建/编辑用户模态框 -->
    <n-modal v-model:show="showCreateModal" preset="dialog" title="用户信息" style="width: 600px;">
      <!-- 根据原版V2Board逻辑，用户创建表单应该简洁 -->
      <n-form ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="120px">
        <!-- 创建用户时显示简洁表单 -->
        <template v-if="!formData.id">
          <n-form-item label="邮箱" path="email_prefix">
            <n-input-group>
              <n-input
                v-model:value="formData.email_prefix"
                placeholder="账号（批量生成请留空）"
                style="width: 45%"
              />
              <n-input
                value="@"
                disabled
                style="width: 10%; text-align: center"
              />
              <n-input
                v-model:value="formData.email_suffix"
                placeholder="域"
                style="width: 45%"
              />
            </n-input-group>
          </n-form-item>

          <n-form-item label="密码" path="password">
            <n-input v-model:value="formData.password" type="password" placeholder="留空则密码与邮箱相同" />
          </n-form-item>

          <n-form-item label="到期时间" path="expired_at">
            <n-date-picker
              v-model:value="formData.expired_at"
              type="datetime"
              placeholder="长期有效"
              clearable
            />
          </n-form-item>

          <n-form-item label="订阅计划" path="plan_id">
            <n-select
              v-model:value="formData.plan_id"
              :options="planOptions"
              placeholder="请选择用户订阅计划"
              clearable
            />
          </n-form-item>

          <n-form-item label="生成数量" path="generate_count" v-if="!formData.email_prefix">
            <n-input-number
              v-model:value="formData.generate_count"
              :max="500"
              placeholder="如果为批量生成请输入生成数量"
            />
          </n-form-item>
        </template>

        <!-- 编辑用户时显示完整表单 -->
        <template v-else>
          <n-form-item label="邮箱" path="email">
            <n-input v-model:value="formData.email" placeholder="请输入邮箱" />
          </n-form-item>

          <n-form-item label="邀请人邮箱" path="invite_user_email">
            <n-input v-model:value="formData.invite_user_email" placeholder="请输入邀请人邮箱" />
          </n-form-item>

          <n-form-item label="已用上行(GB)" path="u">
            <n-input-number v-model:value="formData.u" :min="0" placeholder="已用上行流量" />
          </n-form-item>

          <n-form-item label="已用下行(GB)" path="d">
            <n-input-number v-model:value="formData.d" :min="0" placeholder="已用下行流量" />
          </n-form-item>

          <n-form-item label="流量(GB)" path="transfer_enable">
            <n-input-number v-model:value="formData.transfer_enable" :min="0" placeholder="流量限制" />
          </n-form-item>

          <n-form-item label="设备数限制" path="device_limit">
            <n-input-number v-model:value="formData.device_limit" :min="0" placeholder="留空则不限制" />
          </n-form-item>

          <n-form-item label="到期时间" path="expired_at">
            <n-date-picker
              v-model:value="formData.expired_at"
              type="datetime"
              placeholder="选择到期时间"
              clearable
            />
          </n-form-item>

          <n-form-item label="订阅计划" path="plan_id">
            <n-select
              v-model:value="formData.plan_id"
              :options="planOptions"
              placeholder="请选择用户订阅计划"
              clearable
            />
          </n-form-item>

          <n-form-item label="账户状态" path="banned">
            <n-select
              v-model:value="formData.banned"
              :options="statusOptions"
              placeholder="请选择账户状态"
            />
          </n-form-item>

          <n-form-item label="推荐返利类型" path="commission_type">
            <n-select
              v-model:value="formData.commission_type"
              :options="commissionTypeOptions"
              placeholder="请选择返利类型"
            />
          </n-form-item>

          <n-form-item label="推荐返利比例(%)" path="commission_rate">
            <n-input-number v-model:value="formData.commission_rate" :min="0" :max="100" placeholder="为空则跟随站点设置" />
          </n-form-item>

          <n-form-item label="专享折扣比例(%)" path="discount">
            <n-input-number v-model:value="formData.discount" :min="0" :max="100" placeholder="专享折扣比例" />
          </n-form-item>

          <n-form-item label="限速(Mbps)" path="speed_limit">
            <n-input-number v-model:value="formData.speed_limit" :min="0" placeholder="留空则不限制" />
          </n-form-item>

          <n-form-item label="是否管理员" path="is_admin">
            <n-switch v-model:value="formData.is_admin" />
          </n-form-item>

          <n-form-item label="是否员工" path="is_staff">
            <n-switch v-model:value="formData.is_staff" />
          </n-form-item>

          <n-form-item label="余额" path="balance">
            <n-input-number v-model:value="formData.balance" :min="0" :precision="2" placeholder="账户余额" />
          </n-form-item>

          <n-form-item label="佣金余额" path="commission_balance">
            <n-input-number v-model:value="formData.commission_balance" :min="0" :precision="2" placeholder="佣金余额" />
          </n-form-item>

          <n-form-item label="备注" path="remarks">
            <n-input
              v-model:value="formData.remarks"
              type="textarea"
              :rows="4"
              placeholder="请在这里记录.."
            />
          </n-form-item>
        </template>
      </n-form>

      <template #action>
        <n-space>
          <n-button @click="showCreateModal = false">取消</n-button>
          <n-button type="primary" @click="handleSave" :loading="saving">
            {{ formData.id ? '更新' : '创建' }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 过滤器模态框 -->
    <n-modal v-model:show="showFilterModal" preset="dialog" title="用户过滤器" style="width: 800px;">
      <div class="filter-content">
        <div v-for="(filter, index) in tempFilters" :key="index" class="filter-item">
          <n-space>
            <n-select 
              v-model:value="filter.field" 
              :options="filterFieldOptions" 
              placeholder="选择字段"
              style="width: 150px"
            />
            <n-select 
              v-model:value="filter.operator" 
              :options="getOperatorOptions(filter.field)" 
              placeholder="操作符"
              style="width: 100px"
            />
            <n-input 
              v-if="!isSelectField(filter.field)"
              v-model:value="filter.value" 
              placeholder="值"
              style="width: 200px"
            />
            <n-select 
              v-else
              v-model:value="filter.value" 
              :options="getSelectOptions(filter.field)" 
              placeholder="选择值"
              style="width: 200px"
            />
            <n-button @click="removeFilter(index)" type="error" size="small">
              <template #icon>
                <n-icon><DeleteIcon /></n-icon>
              </template>
            </n-button>
          </n-space>
        </div>
        
        <n-button @click="addFilter" type="dashed" style="width: 100%; margin-top: 16px;">
          <template #icon>
            <n-icon><AddIcon /></n-icon>
          </template>
          添加过滤条件
        </n-button>
      </div>

      <template #action>
        <n-space>
          <n-button @click="clearFilters">清空</n-button>
          <n-button @click="showFilterModal = false">取消</n-button>
          <n-button type="primary" @click="applyFilters">应用</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 发送邮件模态框 -->
    <n-modal v-model:show="showMailModal" preset="dialog" title="发送邮件" style="width: 600px;">
      <n-form ref="mailFormRef" :model="mailForm" label-placement="left" label-width="80px">
        <n-form-item label="主题" path="subject">
          <n-input v-model:value="mailForm.subject" placeholder="邮件主题" />
        </n-form-item>
        
        <n-form-item label="内容" path="content">
          <n-input v-model:value="mailForm.content" type="textarea" :rows="6" placeholder="邮件内容" />
        </n-form-item>
      </n-form>

      <template #action>
        <n-space>
          <n-button @click="showMailModal = false">取消</n-button>
          <n-button type="primary" @click="handleSendMail" :loading="sendingMail">发送</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h } from 'vue'
import { 
  NButton, NCard, NDataTable, NModal, NForm, NFormItem, NInput, NInputNumber,
  NSelect, NDatePicker, NSwitch, NSpace, NIcon, NDropdown, NTag, NBadge,
  useMessage, useDialog
} from 'naive-ui'
import { adminApiClient, type User, type UserRequest, type UserFilter } from '@/api/admin'
import { useAdminStore } from '@/stores/admin'

// 格式化函数
const formatDate = (timestamp: number) => {
  if (!timestamp) return '-'
  // 如果timestamp是秒级时间戳，需要转换为毫秒
  const date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatBytes = (bytes: number) => {
  if (!bytes || bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 图标定义
const AddIcon = () => h('svg', {
  width: '16',
  height: '16',
  viewBox: '0 0 24 24',
  fill: 'none',
  stroke: 'currentColor',
  'stroke-width': '2'
}, [
  h('line', { x1: '12', y1: '5', x2: '12', y2: '19' }),
  h('line', { x1: '5', y1: '12', x2: '19', y2: '12' })
])

const FilterIcon = () => h('svg', {
  width: '16',
  height: '16',
  viewBox: '0 0 24 24',
  fill: 'none',
  stroke: 'currentColor',
  'stroke-width': '2'
}, [
  h('polygon', { points: '22,3 2,3 10,12.46 10,19 14,21 14,12.46' })
])

const MoreIcon = () => h('svg', {
  width: '16',
  height: '16',
  viewBox: '0 0 24 24',
  fill: 'none',
  stroke: 'currentColor',
  'stroke-width': '2'
}, [
  h('circle', { cx: '12', cy: '12', r: '1' }),
  h('circle', { cx: '19', cy: '12', r: '1' }),
  h('circle', { cx: '5', cy: '12', r: '1' })
])

const DeleteIcon = () => h('svg', {
  width: '16',
  height: '16',
  viewBox: '0 0 24 24',
  fill: 'none',
  stroke: 'currentColor',
  'stroke-width': '2'
}, [
  h('polyline', { points: '3,6 5,6 21,6' }),
  h('path', { d: 'm19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2' })
])

// 响应式数据
const message = useMessage()
const dialog = useDialog()
const adminStore = useAdminStore()

const users = ref<User[]>([])
const loading = ref(false)
const saving = ref(false)
const sendingMail = ref(false)

// 分页（从localStorage读取保存的页面大小）
const pagination = reactive({
  page: 1,
  pageSize: parseInt(localStorage.getItem('user_manage_page_size') || '10'),
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100, 150]
})

const paginationConfig = computed(() => {
  const config = {
    page: pagination.page,
    pageSize: pagination.pageSize,
    itemCount: pagination.total,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100, 150],
    onUpdatePage: (page: number) => {
      console.log('分页事件触发，切换到第', page, '页')
      pagination.page = page
      loadUsers()
    },
    onUpdatePageSize: (pageSize: number) => {
      console.log('页面大小变更事件触发，新大小:', pageSize)
      // 保存页面大小到localStorage（与原版保持一致）
      localStorage.setItem('user_manage_page_size', pageSize.toString())
      pagination.pageSize = pageSize
      pagination.page = 1
      loadUsers()
    }
  }

  console.log('分页配置详情:', {
    page: config.page,
    pageSize: config.pageSize,
    itemCount: config.itemCount,
    totalPages: Math.ceil(config.itemCount / config.pageSize),
    showSizePicker: config.showSizePicker
  })
  return config
})

// 排序
const sorter = ref<{ columnKey: string; order: 'ascend' | 'descend' } | null>(null)

// 过滤器
const filters = ref<UserFilter[]>([])
const tempFilters = ref<any[]>([])
const showFilterModal = ref(false)

// 表单
const showCreateModal = ref(false)
const formRef = ref()
const formData = ref<UserRequest>({
  email: '',
  email_prefix: '',
  email_suffix: '',
  password: '',
  transfer_enable: undefined,
  balance: undefined,
  commission_balance: undefined,
  commission_type: undefined,
  commission_rate: undefined,
  discount: undefined,
  banned: undefined,
  is_admin: undefined,
  is_staff: undefined,
  u: undefined,
  d: undefined,
  device_limit: undefined,
  invite_user_email: '',
  speed_limit: undefined,
  remarks: '',
  generate_count: undefined
})

// 邮件表单
const showMailModal = ref(false)
const mailFormRef = ref()
const mailForm = ref({
  subject: '',
  content: ''
})

// 加载数据
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.page,
      pageSize: pagination.pageSize,
      filter: filters.value,
      // 只有在有排序信息时才添加排序参数（注意：参数名与原版保持一致）
      ...(sorter.value?.columnKey && {
        sort_type: sorter.value.order === 'ascend' ? 'ASC' : 'DESC',
        sort: sorter.value.columnKey
      })
    }

    console.log('分页参数:', params)
    console.log('当前页:', pagination.page, '每页大小:', pagination.pageSize)

    const result = await adminApiClient.getUsers(params)
    console.log('API响应结果:', result)
    console.log('用户数据:', result.data?.length, '条')
    console.log('总用户数:', result.total)

    users.value = result.data
    pagination.total = result.total
  } catch (error: any) {
    console.error('获取用户列表失败:', error)

    // 如果是排序导致的错误（502或500），尝试清除排序重新加载
    if ((error.response?.status === 502 || error.response?.status === 500) && sorter.value) {
      message.warning('排序请求失败，已清除排序条件')
      sorter.value = null
      // 递归调用，但不会无限递归因为sorter.value已清空
      setTimeout(() => loadUsers(), 100)
      return
    }

    message.error(error.response?.data?.message || error.message || '获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 自定义排序箭头图标
const createSorterIcon = (order: 'ascend' | 'descend' | false) => {
  const isActive = order !== false
  const isAscend = order === 'ascend'

  return h('div', {
    class: 'custom-sorter-icon',
    style: {
      display: 'inline-flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      width: '12px',
      height: '12px',
      marginLeft: '2px'
    }
  }, [
    // 上箭头
    h('div', {
      style: {
        width: '0',
        height: '0',
        borderLeft: '3px solid transparent',
        borderRight: '3px solid transparent',
        borderBottom: `3px solid ${isActive && isAscend ? '#1890ff' : '#8c8c8c'}`,
        marginBottom: '1px',
        filter: isActive && isAscend ? 'drop-shadow(0 0 2px rgba(24,144,255,0.3))' : 'none'
      }
    }),
    // 下箭头
    h('div', {
      style: {
        width: '0',
        height: '0',
        borderLeft: '3px solid transparent',
        borderRight: '3px solid transparent',
        borderTop: `3px solid ${isActive && !isAscend ? '#1890ff' : '#8c8c8c'}`,
        filter: isActive && !isAscend ? 'drop-shadow(0 0 2px rgba(24,144,255,0.3))' : 'none'
      }
    })
  ])
}

// 表格列定义
const columns = computed(() => [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    sorter: true,
    renderSorterIcon: ({ order }: { order: 'ascend' | 'descend' | false }) => createSorterIcon(order),
    render: (row: User) => row.id
  },
  {
    title: '邮箱',
    key: 'email',
    width: 200,
    render: (row: User) => {
      const isOnline = row.t && (Date.now() / 1000 - row.t) < 600
      return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
        h(NBadge, {
          dot: true,
          type: isOnline ? 'success' : 'default',
          title: row.t ? `最后在线: ${formatDate(row.t)}` : '从未在线'
        }),
        h('span', row.email)
      ])
    }
  },
  {
    title: '状态',
    key: 'banned',
    width: 80,
    sorter: true,
    renderSorterIcon: ({ order }: { order: 'ascend' | 'descend' | false }) => createSorterIcon(order),
    render: (row: User) => h(NTag, {
      type: row.banned ? 'error' : 'success'
    }, { default: () => row.banned ? '封禁' : '正常' })
  },
  {
    title: '订阅',
    key: 'plan_id',
    width: 120,
    sorter: true,
    renderSorterIcon: ({ order }: { order: 'ascend' | 'descend' | false }) => createSorterIcon(order),
    render: (row: User) => row.plan_name || '-'
  },
  {
    title: '权限组',
    key: 'group_id',
    width: 120,
    sorter: true,
    renderSorterIcon: ({ order }: { order: 'ascend' | 'descend' | false }) => createSorterIcon(order),
    render: (row: User) => {
      if (!row.group_id) return '-'
      const group = adminStore.serverGroups?.find(g => g.id === row.group_id)
      return group ? group.name : `组${row.group_id}`
    }
  },
  {
    title: '已用流量',
    key: 'total_used',
    width: 120,
    sorter: true,
    renderSorterIcon: ({ order }: { order: 'ascend' | 'descend' | false }) => createSorterIcon(order),
    render: (row: User) => {
      // 后端返回的u和d是字节数，total_used也是字节数
      const usedBytes = row.total_used || (row.u + row.d)
      const usedGB = usedBytes / (1024 * 1024 * 1024)
      const totalGB = row.transfer_enable / (1024 * 1024 * 1024)
      const isOverLimit = usedGB > totalGB
      return h(NTag, {
        type: isOverLimit ? 'error' : 'success'
      }, { default: () => `${usedGB.toFixed(2)}G / ${totalGB.toFixed(2)}G` })
    }
  },
  {
    title: '总流量',
    key: 'transfer_enable',
    width: 100,
    sorter: true,
    renderSorterIcon: ({ order }: { order: 'ascend' | 'descend' | false }) => createSorterIcon(order),
    render: (row: User) => `${(row.transfer_enable / (1024 * 1024 * 1024)).toFixed(2)}G`
  },
  {
    title: '设备数',
    key: 'device_limit',
    width: 100,
    // 在remote模式下，使用handleSorterChange手动处理排序
    sorter: true,
    renderSorterIcon: ({ order }: { order: 'ascend' | 'descend' | false }) => createSorterIcon(order),
    render: (row: User) => {
      const current = row.alive_ip || 0
      const limit = row.device_limit || '∞'
      return `${current} / ${limit}`
    }
  },
  {
    title: '到期时间',
    key: 'expired_at',
    width: 150,
    sorter: true,
    renderSorterIcon: ({ order }: { order: 'ascend' | 'descend' | false }) => createSorterIcon(order),
    render: (row: User) => {
      if (!row.expired_at) return h(NTag, { type: 'success' }, { default: () => '长期有效' })
      const isExpired = row.expired_at < Date.now() / 1000
      return h(NTag, {
        type: isExpired ? 'error' : 'success'
      }, { default: () => formatDate(row.expired_at!) })
    }
  },
  {
    title: '余额',
    key: 'balance',
    width: 100,
    sorter: true,
    renderSorterIcon: ({ order }: { order: 'ascend' | 'descend' | false }) => createSorterIcon(order),
    render: (row: User) => `¥${(row.balance / 100).toFixed(2)}`
  },
  {
    title: '佣金',
    key: 'commission_balance',
    width: 100,
    sorter: true,
    renderSorterIcon: ({ order }: { order: 'ascend' | 'descend' | false }) => createSorterIcon(order),
    render: (row: User) => `¥${(row.commission_balance / 100).toFixed(2)}`
  },
  {
    title: '加入时间',
    key: 'created_at',
    width: 150,
    sorter: true,
    renderSorterIcon: ({ order }: { order: 'ascend' | 'descend' | false }) => createSorterIcon(order),
    render: (row: User) => formatDate(row.created_at)
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right' as const,
    render: (row: User) => h(NDropdown, {
      options: [
        {
          label: '编辑',
          key: 'edit',
          icon: () => h('i', { class: 'fas fa-edit' })
        },
        {
          label: '复制订阅URL',
          key: 'copy-url',
          icon: () => h('i', { class: 'fas fa-copy' })
        },
        {
          label: '重置UUID',
          key: 'reset-secret',
          icon: () => h('i', { class: 'fas fa-refresh' })
        },
        {
          label: '删除',
          key: 'delete',
          icon: () => h('i', { class: 'fas fa-trash' })
        }
      ],
      onSelect: (key: string) => handleRowAction(key, row)
    }, {
      default: () => h(NButton, { size: 'small' }, { default: () => '操作' })
    })
  }
])

// 选项数据
const planOptions = computed(() => [
  { label: '无订阅', value: null },
  ...adminStore.plans.map(plan => ({
    label: plan.name,
    value: plan.id
  }))
])

const groupOptions = computed(() => [
  { label: '默认组', value: null },
  ...adminStore.serverGroups.map(group => ({
    label: group.name,
    value: group.id
  }))
])

const statusOptions = [
  { label: '正常', value: 0 },
  { label: '封禁', value: 1 }
]

// 批量操作选项
const batchOptions = computed(() => [
  {
    label: '导出CSV',
    key: 'export-csv',
    icon: () => h('i', { class: 'fas fa-file-excel' })
  },
  {
    label: '发送邮件',
    key: 'send-mail',
    icon: () => h('i', { class: 'fas fa-envelope' })
  },
  {
    label: '批量封禁',
    key: 'batch-ban',
    icon: () => h('i', { class: 'fas fa-ban' }),
    disabled: filters.value.length === 0
  },
  {
    label: '批量删除',
    key: 'batch-delete',
    icon: () => h('i', { class: 'fas fa-trash' }),
    disabled: filters.value.length === 0
  }
])

// 过滤器字段选项
const filterFieldOptions = [
  { label: '邮箱', value: 'email' },
  { label: '用户ID', value: 'id' },
  { label: '订阅', value: 'plan_id' },
  { label: '流量', value: 'transfer_enable' },
  { label: '下行', value: 'd' },
  { label: '到期时间', value: 'expired_at' },
  { label: 'UUID', value: 'uuid' },
  { label: 'TOKEN', value: 'token' },
  { label: '账号状态', value: 'banned' },
  { label: '邀请人邮箱', value: 'invite_by_email' },
  { label: '邀请人ID', value: 'invite_user_id' },
  { label: '备注', value: 'remarks' },
  { label: '管理员', value: 'is_admin' }
]

// 表单验证规则
const formRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' }
  ],
  email_prefix: [
    { required: true, message: '请输入邮箱前缀', trigger: 'blur' }
  ],
  email_suffix: [
    { required: true, message: '请输入邮箱后缀', trigger: 'blur' }
  ],
  password: [
    { min: 6, message: '密码至少6位', trigger: 'blur' }
  ]
}

// 事件处理

// 支持的排序字段白名单（根据原版umi.js确定的字段）
// 注意：device_limit字段使用本地排序，不需要发送到服务器
const supportedSortFields = [
  'id', 'banned', 'plan_id', 'group_id', 'total_used',
  'transfer_enable', 'expired_at', 'balance', 'commission_balance', 'created_at'
]

const handleSorterChange = (sorterInfo: any) => {
  console.log('排序信息:', sorterInfo)

  // 处理设备数字段的本地排序
  if (sorterInfo && sorterInfo.columnKey === 'device_limit') {
    console.log('执行设备数本地排序:', sorterInfo.order)

    // 保存排序状态以显示排序指示器
    sorter.value = sorterInfo

    // 手动对用户数据进行排序
    const sortedUsers = [...users.value].sort((a, b) => {
      const aValue = a.alive_ip || 0
      const bValue = b.alive_ip || 0

      if (sorterInfo.order === 'ascend') {
        return aValue - bValue
      } else {
        return bValue - aValue
      }
    })

    // 更新用户数据
    users.value = sortedUsers
    return
  }

  // 验证服务器端排序字段是否支持
  if (sorterInfo && sorterInfo.columnKey && !supportedSortFields.includes(sorterInfo.columnKey)) {
    message.warning(`字段 "${sorterInfo.columnKey}" 暂不支持排序`)
    return
  }

  sorter.value = sorterInfo
  loadUsers()
}

const openCreateModal = () => {
  formData.value = {
    email: '',
    email_prefix: '',
    email_suffix: '',
    password: '',
    transfer_enable: undefined,
    balance: undefined,
    commission_balance: undefined,
    commission_type: undefined,
    commission_rate: undefined,
    discount: undefined,
    banned: undefined,
    is_admin: undefined,
    is_staff: undefined,
    u: undefined,
    d: undefined,
    device_limit: undefined,
    invite_user_email: '',
    speed_limit: undefined,
    remarks: '',
    generate_count: undefined
  }
  showCreateModal.value = true
}

const openEditModal = (user: User) => {
  formData.value = {
    id: user.id,
    email: user.email,
    transfer_enable: user.transfer_enable / (1024 * 1024 * 1024), // 转换为GB
    expired_at: user.expired_at ? user.expired_at * 1000 : undefined, // 转换为毫秒
    plan_id: user.plan_id,
    group_id: user.group_id,
    banned: user.banned,
    is_admin: user.is_admin,
    balance: user.balance / 100, // 转换为元
    commission_balance: user.commission_balance / 100, // 转换为元
    remarks: user.remarks,
    device_limit: user.device_limit
  }
  showCreateModal.value = true
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()
    saving.value = true

    const data = { ...formData.value }

    // 数据转换
    if (data.transfer_enable) {
      data.transfer_enable = data.transfer_enable * 1024 * 1024 * 1024 // 转换为字节
    }
    if (data.expired_at) {
      data.expired_at = Math.floor(data.expired_at / 1000) // 转换为秒
    }
    if (data.balance) {
      data.balance = Math.round(data.balance * 100) // 转换为分
    }
    if (data.commission_balance) {
      data.commission_balance = Math.round(data.commission_balance * 100) // 转换为分
    }

    if (data.id) {
      // 编辑用户 - 使用update方法
      await adminApiClient.saveUser(data)
      message.success('用户更新成功')
    } else {
      // 创建用户 - 使用generate方法，按照原版V2Board逻辑
      const generateData: any = {
        email_prefix: data.email_prefix,
        email_suffix: data.email_suffix,
        password: data.password || undefined, // 留空则后端使用邮箱作为密码
        plan_id: data.plan_id || undefined,
        expired_at: data.expired_at ? Math.floor(data.expired_at / 1000) : undefined, // 转换为秒
        generate_count: data.generate_count || undefined // 生成数量
      }

      await adminApiClient.generateUser(generateData)
      message.success('用户创建成功')
    }

    showCreateModal.value = false
    loadUsers()
  } catch (error: any) {
    message.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

const handleRowAction = async (action: string, user: User) => {
  switch (action) {
    case 'edit':
      openEditModal(user)
      break
    case 'copy-url':
      if (user.subscribe_url) {
        await navigator.clipboard.writeText(user.subscribe_url)
        message.success('订阅URL已复制到剪贴板')
      } else {
        message.warning('该用户没有订阅URL')
      }
      break
    case 'reset-secret':
      dialog.warning({
        title: '确认重置',
        content: '确定要重置该用户的UUID和订阅URL吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
          try {
            await adminApiClient.resetUserSecret(user.id)
            message.success('重置成功')
            loadUsers()
          } catch (error: any) {
            message.error(error.message || '重置失败')
          }
        }
      })
      break
    case 'delete':
      dialog.warning({
        title: '确认删除',
        content: `确定要删除用户 ${user.email} 吗？此操作不可恢复。`,
        positiveText: '删除',
        negativeText: '取消',
        onPositiveClick: async () => {
          try {
            await adminApiClient.deleteUser(user.id)
            message.success('删除成功')
            loadUsers()
          } catch (error: any) {
            message.error(error.message || '删除失败')
          }
        }
      })
      break
  }
}

const handleBatchAction = async (action: string) => {
  switch (action) {
    case 'export-csv':
      try {
        await adminApiClient.exportUsersCSV(filters.value)
        message.success('导出成功')
      } catch (error: any) {
        message.error(error.message || '导出失败')
      }
      break
    case 'send-mail':
      showMailModal.value = true
      break
    case 'batch-ban':
      dialog.warning({
        title: '确认封禁',
        content: '确定要封禁所有过滤的用户吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
          try {
            await adminApiClient.banUsers(filters.value)
            message.success('批量封禁成功')
            loadUsers()
          } catch (error: any) {
            message.error(error.message || '批量封禁失败')
          }
        }
      })
      break
    case 'batch-delete':
      dialog.warning({
        title: '确认删除',
        content: '确定要删除所有过滤的用户吗？此操作不可恢复。',
        positiveText: '删除',
        negativeText: '取消',
        onPositiveClick: async () => {
          try {
            await adminApiClient.deleteAllUsers(filters.value)
            message.success('批量删除成功')
            loadUsers()
          } catch (error: any) {
            message.error(error.message || '批量删除失败')
          }
        }
      })
      break
  }
}

// 过滤器相关
const getOperatorOptions = (field: string) => {
  const numericFields = ['id', 'transfer_enable', 'd', 'expired_at']
  const exactFields = ['uuid', 'token', 'invite_user_id']
  const selectFields = ['plan_id', 'banned', 'is_admin']

  if (numericFields.includes(field)) {
    return [
      { label: '=', value: '=' },
      { label: '>=', value: '>=' },
      { label: '>', value: '>' },
      { label: '<', value: '<' },
      { label: '<=', value: '<=' }
    ]
  } else if (exactFields.includes(field) || selectFields.includes(field)) {
    return [{ label: '=', value: '=' }]
  } else {
    return [{ label: '模糊', value: '模糊' }]
  }
}

const isSelectField = (field: string) => {
  return ['plan_id', 'banned', 'is_admin'].includes(field)
}

const getSelectOptions = (field: string) => {
  switch (field) {
    case 'plan_id':
      return [
        { label: '无订阅', value: 'null' },
        ...adminStore.plans.map(plan => ({
          label: plan.name,
          value: plan.id
        }))
      ]
    case 'banned':
      return [
        { label: '正常', value: 0 },
        { label: '封禁', value: 1 }
      ]
    case 'is_admin':
      return [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    default:
      return []
  }
}

const addFilter = () => {
  tempFilters.value.push({
    field: '',
    operator: '',
    value: ''
  })
}

const removeFilter = (index: number) => {
  tempFilters.value.splice(index, 1)
}

const applyFilters = () => {
  const validFilters = tempFilters.value.filter(f => f.field && f.operator && f.value !== '')

  // 按照原版V2Board的格式：数组中每个元素包含 key, condition, value
  filters.value = validFilters.map(f => ({
    key: f.field,
    condition: f.operator,
    value: f.value
  }))

  showFilterModal.value = false
  pagination.page = 1
  loadUsers()
}

const clearFilters = () => {
  tempFilters.value = []
  filters.value = []
  showFilterModal.value = false
  pagination.page = 1
  loadUsers()
}

// 发送邮件
const handleSendMail = async () => {
  if (!mailForm.value.subject || !mailForm.value.content) {
    message.warning('请填写邮件主题和内容')
    return
  }

  try {
    sendingMail.value = true
    await adminApiClient.sendMailToUsers(
      filters.value,
      mailForm.value.subject,
      mailForm.value.content
    )
    message.success('邮件发送成功')
    showMailModal.value = false
    mailForm.value = { subject: '', content: '' }
  } catch (error: any) {
    message.error(error.message || '邮件发送失败')
  } finally {
    sendingMail.value = false
  }
}

onMounted(async () => {
  loadUsers()
  adminStore.loadPlans()
  await adminStore.loadServerGroups()
  console.log('权限组数据:', adminStore.serverGroups)
})
</script>

<style scoped>
.user-management {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color-1);
}

.action-bar {
  margin-bottom: 16px;
}

.filter-content {
  max-height: 400px;
  overflow-y: auto;
}

.filter-item {
  margin-bottom: 12px;
}

.filter-item:last-child {
  margin-bottom: 0;
}

/* 调整表格样式，使其更接近原版Ant Design风格 */
:deep(.n-data-table) {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

:deep(.n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
  border-bottom: 1px solid #f0f0f0;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  padding: 16px;
}

/* 自定义排序箭头样式 - 已通过renderSorterIcon实现 */

/* 表头布局调整 - 确保文字和箭头紧挨着 */
:deep(.n-data-table-th) {
  white-space: nowrap;
}

:deep(.n-data-table-th .n-data-table-th__title) {
  display: inline-flex !important;
  align-items: center;
  gap: 2px !important;
}

/* 移除默认间距 */
:deep(.n-data-table-th .n-data-table-th__title-wrapper) {
  display: inline-flex !important;
  align-items: center;
  gap: 2px !important;
}

/* 表格行样式 */
:deep(.n-data-table-tbody .n-data-table-tr) {
  transition: background-color 0.3s;
}

:deep(.n-data-table-tbody .n-data-table-tr:hover) {
  background-color: #fafafa;
}

:deep(.n-data-table-td) {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}

/* 表格头部可排序列的鼠标样式 */
:deep(.n-data-table-th--sortable) {
  cursor: pointer;
  user-select: none;
}

:deep(.n-data-table-th--sortable:hover) {
  background-color: #f5f5f5;
}
</style>
