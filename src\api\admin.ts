import axios, { type AxiosInstance, type AxiosResponse } from 'axios'

// 管理员API响应类型
export interface AdminApiResponse<T = any> {
  data: T
  message?: string
  status?: string
}

// 签到配置类型
export interface AdminCheckinConfig {
  id?: number
  plan_id?: number
  plan_name?: string
  reward_mode: 'fixed' | 'random'
  daily_traffic: number
  min_traffic?: number
  max_traffic?: number
  consecutive_bonus: number
  consecutive_days: number
  enabled: boolean
  created_at?: number
  updated_at?: number
}

// 节点选择类型
export interface NodeSelection {
  server_type: string
  server_id: number
}

// 权限组接口
export interface ServerGroup {
  id: number
  name: string
  user_count: number
  server_count: number
  created_at?: number
  updated_at?: number
}

// 权限组创建/更新请求
export interface ServerGroupRequest {
  id?: number
  name: string
}

// 公告相关类型 - 匹配V2Board数据库字段
export interface Notice {
  id?: number
  title: string
  content: string
  tags?: string[]
  img_url?: string
  show: number  // 数据库中是tinyint(1)，0=隐藏，1=显示
  created_at?: number
  updated_at?: number
}

// 公告API响应类型 - 匹配V2Board后端格式
export interface NoticeListResponse {
  data: Notice[]
}

export interface NoticeOperationResponse {
  data: boolean
}

// 工单相关类型 - 匹配V2Board数据库字段
export interface Ticket {
  id?: number
  user_id: number
  subject: string
  level: number  // 0=低, 1=中, 2=高
  status: number  // 0=开启, 1=关闭
  reply_status: number  // 0=待回复, 1=已回复
  created_at?: number
  updated_at?: number
  message?: TicketMessage[]  // 工单详情时包含消息列表
}

export interface TicketMessage {
  id?: number
  ticket_id: number
  user_id: number
  message: string
  is_me?: boolean  // 是否为管理员消息
  created_at?: number
  updated_at?: number
}

// 工单API响应类型 - 匹配V2Board后端格式
export interface TicketListResponse {
  data: Ticket[]
  total: number
}

export interface TicketDetailResponse {
  data: Ticket
}

export interface TicketOperationResponse {
  data: boolean
}

// 用户相关类型
export interface User {
  id: number
  email: string
  password?: string
  password_algo?: string
  password_salt?: string
  balance: number
  discount?: number
  commission_type?: number
  commission_rate?: number
  commission_balance: number
  t: number
  u: number
  d: number
  transfer_enable: number
  banned: number
  is_admin: number
  is_staff?: number
  last_login_at?: number
  last_login_ip?: string
  uuid: string
  group_id?: number
  plan_id?: number
  remind_expire?: number
  remind_traffic?: number
  token: string
  remarks?: string
  subscribe_url?: string
  reset_day?: number
  expired_at?: number
  plan_name?: string
  group_name?: string
  total_used?: number
  device_limit?: number
  alive_ip?: number
  ips?: string
  invite_user_id?: number
  invite_by_email?: string
  created_at: number
  updated_at: number
}

// 用户创建/更新请求
export interface UserRequest {
  id?: number
  email: string
  email_prefix?: string
  email_suffix?: string
  password?: string
  transfer_enable?: number
  expired_at?: number
  plan_id?: number
  group_id?: number
  banned?: number
  is_admin?: number
  is_staff?: number
  balance?: number
  commission_balance?: number
  commission_type?: number
  commission_rate?: number
  discount?: number
  remarks?: string
  u?: number
  d?: number
  device_limit?: number
  invite_user_email?: string
  speed_limit?: number
  generate_count?: number
}

// 用户过滤器 - 按照原版V2Board格式
export interface UserFilter {
  key: string
  condition: string
  value: string | number
}

// 优惠券相关类型
export interface Coupon {
  id: number
  show: boolean
  name: string
  type: number // 1=金额优惠, 2=比例优惠
  code: string
  value: number
  limit_use: number | null
  limit_use_with_user: number | null
  limit_plan_ids: string[] | null
  limit_period: string[] | null
  started_at: number
  ended_at: number
  created_at?: string
  updated_at?: string
}

export interface CouponRequest {
  id?: number
  name: string
  code?: string
  type: number
  value: number
  started_at: number | null
  ended_at: number | null
  limit_use?: number | null
  limit_use_with_user?: number | null
  limit_plan_ids?: string[] | null
  limit_period?: string[] | null
  generate_count?: number
}

// 礼品卡相关类型
export interface GiftCard {
  id: number
  name: string
  type: number // 1=金额, 2=时长, 3=流量, 4=重置, 5=套餐
  code: string
  value: number
  plan_id: number | null
  limit_use: number | null
  started_at: number
  ended_at: number
  created_at?: string
  updated_at?: string
}

export interface GiftCardRequest {
  id?: number
  name: string
  code?: string
  type: number
  value: number
  plan_id?: number | null
  started_at: number | null
  ended_at: number | null
  limit_use?: number | null
  generate_count?: number
}

// 服务器节点接口
export interface ServerNode {
  id: number
  name: string
  type: string
  host: string
  port: number | string
  show: boolean
  sort: number
  group_id: number[]
  tags?: string[]
  rate?: number
  created_at?: number
  updated_at?: number
  parent_id?: number
  route_id?: number[]
  last_check_at?: number

  // 通用字段
  server_port?: number
  network?: string
  remarks?: string

  // Shadowsocks 特有字段
  cipher?: string
  obfs?: string
  obfs_settings?: any

  // VMess 特有字段
  tls?: number
  networkSettings?: any
  tlsSettings?: any
  dnsSettings?: any
  ruleSettings?: any
  v2ray_json?: any

  // Trojan 特有字段
  network_settings?: any
  allow_insecure?: number
  server_name?: string

  // VLESS 特有字段
  tls_settings?: any
  flow?: string

  // Hysteria 特有字段
  version?: number
  up_mbps?: number
  down_mbps?: number
  obfs_password?: string
  insecure?: number

  // AnyTLS 特有字段
  padding_scheme?: any

  // UI 状态字段
  updating?: boolean
  deleting?: boolean
}

// 流量倍率配置类型
export interface TrafficRateConfig {
  id?: number
  name: string
  status: boolean
  start_time: string  // 每日开始时间 HH:MM:SS
  end_time: string    // 每日结束时间 HH:MM:SS
  days_of_week?: string // 生效星期 "1,2,3,4,5,6,7"
  target_rate: number
  node_filter: 'all' | 'include' | 'exclude'
  node_ids?: NodeSelection[]
  node_filter_desc?: string
  backup_enabled: boolean
  auto_restore: boolean
  description?: string
  telegram_notify_enabled?: boolean
  is_active?: boolean
  latest_log?: TrafficRateLog
  created_at?: string
}

// 流量倍率执行记录类型
export interface TrafficRateLog {
  id: number
  config_name: string
  execution_type: 'start' | 'end'
  execution_type_desc: string
  target_rate: number
  affected_nodes: number
  status: 'success' | 'failed' | 'processing'
  status_desc: string
  status_color: string
  executed_at: string
  error_message?: string
  duration: number
}



// 自动限速配置类型
export interface AutoSpeedlimitConfig {
  id?: number
  enable: boolean
  traffic_mode: 'daily' | 'total' | 'both'
  daily_calc_mode: 'total' | 'remaining'
  threshold_1?: number
  speed_1?: number
  threshold_2?: number
  speed_2?: number
  threshold_3?: number
  speed_3?: number
  threshold_4?: number
  speed_4?: number
  threshold_5?: number
  speed_5?: number
  created_at?: number
  updated_at?: number
}

// 自动限速日志类型
export interface AutoSpeedlimitLog {
  id: number
  user_id: number
  user_email: string
  action: 'limit' | 'restore'
  action_desc: string
  old_status: number
  new_status: number
  old_status_desc: string
  new_status_desc: string
  old_speedlimit?: number
  new_speedlimit?: number
  old_speedlimit_desc: string
  new_speedlimit_desc: string
  trigger_info?: string
  daily_percent?: number
  total_percent?: number
  created_at: number
}

// 被限速用户类型
export interface LimitedUser {
  id: number
  email: string
  auto_speedlimit_status: number
  speed_limit?: number
  original_speedlimit?: number
  daily_percent: number
  total_percent: number
  transfer_enable: number
  u: number
  d: number
  today_used: number
  remaining: number
}

// 自动限速统计类型
export interface AutoSpeedlimitStats {
  config_enabled: boolean
  current_limited_users: number
  recent_stats: {
    limit_operations: number
    restore_operations: number
    limited_users: number
    restored_users: number
  }
  config_summary?: {
    enabled: boolean
    traffic_mode: string
    daily_calc_mode: string
    levels_count: number
    levels: Array<{
      level: number
      threshold: number
      speed: number
    }>
  }
}

// 流量倍率统计类型
export interface TrafficRateStats {
  configs: {
    total: number
    active: number
    inactive: number
    currently_running: number
  }
  executions: {
    total: number
    success: number
    failed: number
    processing: number
    start_executions: number
    end_executions: number
  }
  backups: {
    total: number
    restored: number
    pending: number
    by_type: Record<string, number>
  }
}

// 套餐类型
export interface Plan {
  id: number
  name: string
  price: number
  transfer_enable: number
  month_price?: number
  quarter_price?: number
  half_year_price?: number
  year_price?: number
  two_year_price?: number
  three_year_price?: number
  onetime_price?: number
  reset_price?: number
  capacity_limit?: number
  speed_limit?: number
  device_limit?: number
  group_id?: number
  reset_traffic_method?: number | null
  purchase_limit_count?: number
  show: boolean
  sort?: number
  renew?: boolean
  content?: string
  created_at?: number
  updated_at?: number
}

// 权限组类型
export interface ServerGroup {
  id: number
  name: string
}

// 套餐创建请求
export interface CreatePlanRequest {
  name: string
  content?: string
  group_id: number
  transfer_enable: number
  device_limit?: number
  month_price?: number
  quarter_price?: number
  half_year_price?: number
  year_price?: number
  two_year_price?: number
  three_year_price?: number
  onetime_price?: number
  reset_price?: number
  reset_traffic_method?: number | null
  capacity_limit?: number
  speed_limit?: number
  purchase_limit_count?: number
}

// 套餐更新请求
export interface UpdatePlanRequest extends CreatePlanRequest {
  id: number
}

// 签到配置请求类型
export interface CreateCheckinConfigRequest {
  plan_id?: number
  reward_mode: 'fixed' | 'random'
  daily_traffic?: number
  min_traffic?: number
  max_traffic?: number
  consecutive_bonus?: number
  consecutive_days?: number
  enabled: boolean
}

export interface UpdateCheckinConfigRequest extends CreateCheckinConfigRequest {
  id: number
}

// 抽奖配置类型
export interface AdminLotteryConfig {
  id?: number
  name: string
  status: boolean
  start_time: string
  frequency: number
  winner_count: number
  reward_type: 'balance' | 'traffic'
  reward_amount: number
  cooldown_rounds: number
  min_balance?: number
  // Telegram 通知配置
  telegram_enabled?: boolean
  telegram_bot_token?: string
  telegram_chat_id?: string
  // 格式化字段
  reward_amount_formatted?: string
  min_balance_formatted?: string
  status_text?: string
  reward_type_text?: string
  created_at?: number
  updated_at?: number
}

// 抽奖记录类型
export interface AdminLotteryLog {
  id: number
  config_id: number
  round_number: string
  total_participants: number
  winner_count: number
  total_reward: number
  executed_at: number
  status: 'success' | 'failed' | 'processing'
  error_message?: string
  total_reward_formatted?: string
  status_text?: string
  executed_at_formatted?: string
  config?: AdminLotteryConfig
}

// 中奖记录类型
export interface AdminLotteryWinner {
  id: number
  lottery_log_id: number
  user_id: number
  reward_type: 'balance' | 'traffic'
  reward_amount: number
  round_number: string
  created_at: number
  reward_amount_formatted?: string
  reward_type_text?: string
  created_at_formatted?: string
  user?: any
  lottery_log?: AdminLotteryLog
}

// 抽奖统计类型
export interface AdminLotteryStatistics {
  total_lotteries: number
  success_lotteries: number
  success_rate: number
  total_participants: number
  total_winners: number
  total_rewards: number
  total_rewards_formatted: string
  avg_participants: number
  avg_winners: number
  today_lotteries: number
  today_winners: number
  today_rewards: number
  total_configs: number
  enabled_configs: number
  balance_configs: number
  traffic_configs: number
}

// 抽奖配置请求类型
export interface CreateLotteryConfigRequest {
  name: string
  status: boolean
  start_time: string
  frequency: number
  winner_count: number
  reward_type: 'balance' | 'traffic'
  reward_amount: number
  cooldown_rounds: number
  min_balance?: number
  // Telegram 通知配置
  telegram_enabled?: boolean
  telegram_bot_token?: string
  telegram_chat_id?: string
}

export interface UpdateLotteryConfigRequest extends CreateLotteryConfigRequest {
  id: number
}

class AdminApiClient {
  private instance: AxiosInstance
  private baseURL: string

  constructor() {
    this.baseURL = ''
    this.instance = axios.create({
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加管理员认证token（使用专门的管理员token存储）
        const adminToken = localStorage.getItem('admin_authorization')
        if (adminToken) {
          // 使用与现有管理员后台相同的格式
          config.headers.authorization = adminToken
        }
        console.log('请求头:', config.headers)
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<AdminApiResponse>) => {
        return response
      },
      (error) => {
        if (error.response?.status === 401 || error.response?.status === 403) {
          console.log('🔒 管理员API检测到认证失败，状态码:', error.response?.status)

          // 清除管理员token并跳转到管理员登录
          localStorage.removeItem('admin_authorization')
          localStorage.removeItem('authorization')
          localStorage.removeItem('admin_token')

          const adminPath = (window as any).V2BOARD_ADMIN_PATH ||
                            localStorage.getItem('v2board_admin_path')

          if (adminPath) {
            console.log('🔒 跳转到管理员登录页:', `/${adminPath}`)
            window.location.href = `/${adminPath}`
          } else {
            console.error('🔒 无法获取管理员路径，跳转到首页')
            window.location.href = '/'
          }
        }
        return Promise.reject(error)
      }
    )
  }

  // 设置基础URL
  setBaseURL(url: string) {
    this.baseURL = url.replace(/\/$/, '')
    this.instance.defaults.baseURL = this.baseURL
  }

  // 获取管理员路径前缀
  private getAdminPrefix(): string {
    // 使用配置中的管理员路径前缀
    const prefix = (window as any).V2BOARD_ADMIN_PATH ||
                   (window as any).settings?.secure_path ||
                   localStorage.getItem('v2board_admin_path')

    if (!prefix) {
      throw new Error('无法获取管理员路径前缀，请检查配置')
    }

    console.log('管理员路径前缀:', prefix)
    return prefix
  }

  // 签到配置管理接口

  // 获取签到配置列表
  async getCheckinConfigs(): Promise<AdminCheckinConfig[]> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/checkin/configs`
      console.log('请求URL:', url)
      console.log('当前token:', localStorage.getItem('admin_authorization'))

      const response = await this.instance.get<AdminApiResponse<AdminCheckinConfig[]>>(url)
      console.log('签到配置响应:', response.data)
      return response.data.data || []
    } catch (error: any) {
      console.error('获取签到配置失败:', error)
      console.error('错误详情:', error.response?.data)
      console.error('错误状态:', error.response?.status)
      throw error
    }
  }

  // 创建签到配置
  async createCheckinConfig(data: CreateCheckinConfigRequest): Promise<AdminCheckinConfig> {
    try {
      const response = await this.instance.post<AdminApiResponse<AdminCheckinConfig>>(
        `/api/v1/${this.getAdminPrefix()}/checkin/saveConfig`,
        data
      )
      return response.data.data
    } catch (error: any) {
      console.error('创建签到配置失败:', error)
      console.error('错误详情:', JSON.stringify(error.response?.data, null, 2))
      console.error('错误状态:', error.response?.status)
      if (error.response?.data?.errors) {
        console.error('验证错误详情:', JSON.stringify(error.response.data.errors, null, 2))
      }
      throw error
    }
  }

  // 更新签到配置
  async updateCheckinConfig(id: number, data: Partial<CreateCheckinConfigRequest>): Promise<AdminCheckinConfig> {
    try {
      const response = await this.instance.post<AdminApiResponse<AdminCheckinConfig>>(
        `/api/v1/${this.getAdminPrefix()}/checkin/saveConfig`,
        { ...data, id }
      )
      return response.data.data
    } catch (error: any) {
      console.error('更新签到配置失败:', error)
      throw error
    }
  }

  // 删除签到配置
  async deleteCheckinConfig(id: number): Promise<void> {
    try {
      const response = await this.instance.post<AdminApiResponse<{success: boolean, message: string}>>(
        `/api/v1/${this.getAdminPrefix()}/checkin/deleteConfig`,
        { id }
      )

      // 检查业务逻辑是否成功
      if (response.data.data && !response.data.data.success) {
        throw new Error(response.data.data.message)
      }
    } catch (error: any) {
      console.error('删除签到配置失败:', error)
      throw error
    }
  }

  // 获取套餐列表
  async getPlans(): Promise<Plan[]> {
    try {
      const response = await this.instance.get<AdminApiResponse<Plan[]>>(
        `/api/v1/${this.getAdminPrefix()}/checkin/plans`
      )
      return response.data.data
    } catch (error: any) {
      console.error('获取套餐列表失败:', error)
      throw error
    }
  }

  // 获取签到统计数据
  async getCheckinStats(): Promise<any> {
    try {
      const response = await this.instance.get<AdminApiResponse<any>>(
        `/api/v1/${this.getAdminPrefix()}/checkin/stats`
      )
      return response.data.data
    } catch (error: any) {
      console.error('获取签到统计失败:', error)
      throw error
    }
  }

  // 获取签到记录列表
  async getCheckinLogs(params?: {
    page?: number
    limit?: number
    user_id?: number
    start_date?: string
    end_date?: string
    with_user?: boolean
  }): Promise<any> {
    try {
      const response = await this.instance.get<AdminApiResponse<any>>(
        `/api/v1/${this.getAdminPrefix()}/checkin/logs`,
        { params }
      )
      return response.data.data
    } catch (error: any) {
      console.error('获取签到记录失败:', error)
      throw error
    }
  }

  // 管理员登录
  async login(data: { email: string; password: string; captcha?: string }): Promise<{ token: string }> {
    try {
      const response = await this.instance.post<AdminApiResponse<{ token: string }>>(
        '/api/v1/passport/auth/login',
        data
      )
      return response.data.data
    } catch (error: any) {
      console.error('管理员登录失败:', error)
      throw error
    }
  }

  // 获取管理员信息
  async getAdminInfo(): Promise<any> {
    try {
      const response = await this.instance.get<AdminApiResponse<any>>(
        `/api/v1/${this.getAdminPrefix()}/user/info`
      )
      return response.data.data
    } catch (error: any) {
      console.error('获取管理员信息失败:', error)
      throw error
    }
  }

  // 抽奖配置管理接口

  // 获取抽奖配置列表
  async getLotteryConfigs(): Promise<AdminLotteryConfig[]> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/lottery/configs`
      console.log('请求URL:', url)

      const response = await this.instance.get<AdminApiResponse<AdminLotteryConfig[]>>(url)
      console.log('抽奖配置响应类型:', typeof response.data)
      console.log('抽奖配置响应内容:', response.data)

      // 检查响应是否是HTML（说明路由错误）
      if (typeof response.data === 'string' && (response.data as string).includes('<template>')) {
        console.error('API返回了HTML而不是JSON，可能是路由配置问题')
        throw new Error('API路由配置错误：返回了前端页面而不是API数据')
      }

      return response.data.data || []
    } catch (error: any) {
      console.error('获取抽奖配置失败:', error)
      throw error
    }
  }

  // 创建抽奖配置
  async createLotteryConfig(data: CreateLotteryConfigRequest): Promise<AdminLotteryConfig> {
    try {
      const response = await this.instance.post<AdminApiResponse<AdminLotteryConfig>>(
        `/api/v1/${this.getAdminPrefix()}/lottery/configs`,
        data
      )
      return response.data.data
    } catch (error: any) {
      console.error('创建抽奖配置失败:', error)
      throw error
    }
  }

  // 更新抽奖配置
  async updateLotteryConfig(id: number, data: CreateLotteryConfigRequest): Promise<AdminLotteryConfig> {
    try {
      const response = await this.instance.put<AdminApiResponse<AdminLotteryConfig>>(
        `/api/v1/${this.getAdminPrefix()}/lottery/configs/${id}`,
        data
      )
      return response.data.data
    } catch (error: any) {
      console.error('更新抽奖配置失败:', error)
      throw error
    }
  }

  // 删除抽奖配置
  async deleteLotteryConfig(id: number): Promise<boolean> {
    try {
      const response = await this.instance.delete<AdminApiResponse<boolean>>(
        `/api/v1/${this.getAdminPrefix()}/lottery/configs/${id}`
      )
      return response.data.data
    } catch (error: any) {
      console.error('删除抽奖配置失败:', error)
      throw error
    }
  }

  // 获取单个抽奖配置
  async getLotteryConfig(id: number): Promise<AdminLotteryConfig> {
    try {
      const response = await this.instance.get<AdminApiResponse<AdminLotteryConfig>>(
        `/api/v1/${this.getAdminPrefix()}/lottery/configs/${id}`
      )
      return response.data.data
    } catch (error: any) {
      console.error('获取抽奖配置详情失败:', error)
      throw error
    }
  }

  // 手动执行抽奖
  async executeLottery(id: number): Promise<any> {
    try {
      const response = await this.instance.post<AdminApiResponse<any>>(
        `/api/v1/${this.getAdminPrefix()}/lottery/execute/${id}`
      )
      return response.data.data
    } catch (error: any) {
      console.error('执行抽奖失败:', error)
      throw error
    }
  }

  // 获取抽奖记录列表
  async getLotteryLogs(params?: {
    page?: number
    limit?: number
    config_id?: number
    status?: string
    start_date?: string
    end_date?: string
  }): Promise<{ data: AdminLotteryLog[], total: number, page: number, limit: number }> {
    try {
      const response = await this.instance.get<AdminApiResponse<any>>(
        `/api/v1/${this.getAdminPrefix()}/lottery/logs`,
        { params }
      )
      return response.data.data
    } catch (error: any) {
      console.error('获取抽奖记录失败:', error)
      throw error
    }
  }

  // 获取中奖记录列表
  async getLotteryWinners(params?: {
    page?: number
    limit?: number
    user_id?: number
    reward_type?: string
    round_number?: string
    start_date?: string
    end_date?: string
  }): Promise<{ data: AdminLotteryWinner[], total: number, page: number, limit: number }> {
    try {
      const response = await this.instance.get<AdminApiResponse<any>>(
        `/api/v1/${this.getAdminPrefix()}/lottery/winners`,
        { params }
      )
      return response.data.data
    } catch (error: any) {
      console.error('获取中奖记录失败:', error)
      throw error
    }
  }

  // 获取抽奖统计数据
  async getLotteryStatistics(configId?: number): Promise<AdminLotteryStatistics> {
    try {
      const params = configId ? { config_id: configId } : undefined
      const response = await this.instance.get<AdminApiResponse<AdminLotteryStatistics>>(
        `/api/v1/${this.getAdminPrefix()}/lottery/statistics`,
        { params }
      )
      return response.data.data
    } catch (error: any) {
      console.error('获取抽奖统计失败:', error)
      throw error
    }
  }

  // 获取可执行的抽奖配置
  async getExecutableLotteryConfigs(): Promise<AdminLotteryConfig[]> {
    try {
      const response = await this.instance.get<AdminApiResponse<AdminLotteryConfig[]>>(
        `/api/v1/${this.getAdminPrefix()}/lottery/executable`
      )
      return response.data.data || []
    } catch (error: any) {
      console.error('获取可执行配置失败:', error)
      throw error
    }
  }

  // ==================== 流量倍率配置相关 API ====================

  // 获取流量倍率配置列表
  async getTrafficRateConfigs(): Promise<AdminApiResponse<TrafficRateConfig[]>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/traffic-rate/configs`
      const response: AxiosResponse<AdminApiResponse<TrafficRateConfig[]>> = await this.instance.get(url)
      return response.data
    } catch (error: any) {
      console.error('获取流量倍率配置失败:', error)
      throw error
    }
  }

  // 创建流量倍率配置
  async createTrafficRateConfig(config: Omit<TrafficRateConfig, 'id' | 'created_at' | 'is_active' | 'latest_log'>): Promise<AdminApiResponse<TrafficRateConfig>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/traffic-rate/configs`
      const response: AxiosResponse<AdminApiResponse<TrafficRateConfig>> = await this.instance.post(url, config)
      return response.data
    } catch (error: any) {
      console.error('创建流量倍率配置失败:', error)
      throw error
    }
  }

  // 更新流量倍率配置
  async updateTrafficRateConfig(id: number, config: Omit<TrafficRateConfig, 'id' | 'created_at' | 'is_active' | 'latest_log'>): Promise<AdminApiResponse<TrafficRateConfig>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/traffic-rate/configs/${id}`
      const response: AxiosResponse<AdminApiResponse<TrafficRateConfig>> = await this.instance.put(url, config)
      return response.data
    } catch (error: any) {
      console.error('更新流量倍率配置失败:', error)
      throw error
    }
  }

  // 删除流量倍率配置
  async deleteTrafficRateConfig(id: number): Promise<AdminApiResponse<void>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/traffic-rate/configs/${id}`
      const response: AxiosResponse<AdminApiResponse<void>> = await this.instance.delete(url)
      return response.data
    } catch (error: any) {
      console.error('删除流量倍率配置失败:', error)
      throw error
    }
  }

  // 切换配置状态
  async toggleTrafficRateStatus(id: number): Promise<AdminApiResponse<TrafficRateConfig>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/traffic-rate/configs/${id}/toggle`
      const response: AxiosResponse<AdminApiResponse<TrafficRateConfig>> = await this.instance.post(url)
      return response.data
    } catch (error: any) {
      console.error('切换配置状态失败:', error)
      throw error
    }
  }

  // 手动执行配置
  async executeTrafficRateConfig(id: number): Promise<AdminApiResponse<any>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/traffic-rate/configs/${id}/execute`
      const response: AxiosResponse<AdminApiResponse<any>> = await this.instance.post(url)
      return response.data
    } catch (error: any) {
      console.error('执行配置失败:', error)
      throw error
    }
  }

  // 手动恢复配置
  async restoreTrafficRateConfig(id: number): Promise<AdminApiResponse<any>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/traffic-rate/configs/${id}/restore`
      const response: AxiosResponse<AdminApiResponse<any>> = await this.instance.post(url)
      return response.data
    } catch (error: any) {
      console.error('恢复配置失败:', error)
      throw error
    }
  }

  // 获取执行记录
  async getTrafficRateLogs(params?: { config_id?: number; limit?: number }): Promise<AdminApiResponse<TrafficRateLog[]>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/traffic-rate/logs`
      const response: AxiosResponse<AdminApiResponse<TrafficRateLog[]>> = await this.instance.get(url, { params })
      return response.data
    } catch (error: any) {
      console.error('获取执行记录失败:', error)
      throw error
    }
  }

  // 获取统计数据
  async getTrafficRateStats(): Promise<AdminApiResponse<TrafficRateStats>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/traffic-rate/stats`
      const response: AxiosResponse<AdminApiResponse<TrafficRateStats>> = await this.instance.get(url)
      return response.data
    } catch (error: any) {
      console.error('获取统计数据失败:', error)
      throw error
    }
  }

  // 获取可用节点列表
  async getAvailableNodes(): Promise<AdminApiResponse<ServerNode[]>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/traffic-rate/nodes`
      const response: AxiosResponse<AdminApiResponse<ServerNode[]>> = await this.instance.get(url)
      return response.data
    } catch (error: any) {
      console.error('获取节点列表失败:', error)
      throw error
    }
  }

  // 清理过期备份
  async cleanupTrafficRateBackups(): Promise<AdminApiResponse<{ deleted_count: number }>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/traffic-rate/cleanup`
      const response: AxiosResponse<AdminApiResponse<{ deleted_count: number }>> = await this.instance.post(url)
      return response.data
    } catch (error: any) {
      console.error('清理备份失败:', error)
      throw error
    }
  }



  // ==================== 节点管理 API ====================

  // 获取所有节点
  async getAllNodes(): Promise<AdminApiResponse<ServerNode[]>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/manage/getNodes`
      const response: AxiosResponse<AdminApiResponse<ServerNode[]>> = await this.instance.get(url)
      return response.data
    } catch (error: any) {
      console.error('获取节点列表失败:', error)
      throw error
    }
  }

  // 保存节点排序
  async saveNodeSort(sortData: Record<string, Record<number, number>>): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/manage/sort`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, sortData)
      return response.data
    } catch (error: any) {
      console.error('保存节点排序失败:', error)
      throw error
    }
  }

  // 节点排序
  async sortNodes(data: { ids: number[] }): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/manage/sort`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('节点排序失败:', error)
      throw error
    }
  }

  // Shadowsocks 节点管理
  async saveShadowsocksNode(data: any): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/shadowsocks/save`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('保存Shadowsocks节点失败:', error)
      throw error
    }
  }

  async deleteShadowsocksNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/shadowsocks/drop`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('删除Shadowsocks节点失败:', error)
      throw error
    }
  }

  async updateShadowsocksNode(data: { id: number; show: boolean | number }): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/shadowsocks/update`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('更新Shadowsocks节点失败:', error)
      throw error
    }
  }

  async copyShadowsocksNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/shadowsocks/copy`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('复制Shadowsocks节点失败:', error)
      throw error
    }
  }

  // VMess 节点管理
  async saveVmessNode(data: any): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/vmess/save`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('保存VMess节点失败:', error)
      throw error
    }
  }

  async deleteVmessNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/vmess/drop`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('删除VMess节点失败:', error)
      throw error
    }
  }

  async updateVmessNode(data: { id: number; show: boolean | number }): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/vmess/update`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('更新VMess节点失败:', error)
      throw error
    }
  }

  async copyVmessNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/vmess/copy`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('复制VMess节点失败:', error)
      throw error
    }
  }

  // Trojan 节点管理
  async saveTrojanNode(data: any): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/trojan/save`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('保存Trojan节点失败:', error)
      throw error
    }
  }

  async deleteTrojanNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/trojan/drop`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('删除Trojan节点失败:', error)
      throw error
    }
  }

  async updateTrojanNode(data: { id: number; show: boolean | number }): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/trojan/update`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('更新Trojan节点失败:', error)
      throw error
    }
  }

  async copyTrojanNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/trojan/copy`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('复制Trojan节点失败:', error)
      throw error
    }
  }

  // VLESS 节点管理
  async saveVlessNode(data: any): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/vless/save`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('保存VLESS节点失败:', error)
      throw error
    }
  }

  async deleteVlessNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/vless/drop`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('删除VLESS节点失败:', error)
      throw error
    }
  }

  async updateVlessNode(data: { id: number; show: boolean | number }): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/vless/update`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('更新VLESS节点失败:', error)
      throw error
    }
  }

  async copyVlessNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/vless/copy`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('复制VLESS节点失败:', error)
      throw error
    }
  }

  // Hysteria 节点管理
  async saveHysteriaNode(data: any): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/hysteria/save`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('保存Hysteria节点失败:', error)
      throw error
    }
  }

  async deleteHysteriaNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/hysteria/drop`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('删除Hysteria节点失败:', error)
      throw error
    }
  }

  async updateHysteriaNode(data: { id: number; show: boolean | number }): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/hysteria/update`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('更新Hysteria节点失败:', error)
      throw error
    }
  }

  async copyHysteriaNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/hysteria/copy`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('复制Hysteria节点失败:', error)
      throw error
    }
  }

  // AnyTLS 节点管理
  async saveAnytlsNode(data: any): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/anytls/save`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('保存AnyTLS节点失败:', error)
      throw error
    }
  }

  async deleteAnytlsNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/anytls/drop`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('删除AnyTLS节点失败:', error)
      throw error
    }
  }

  async updateAnytlsNode(data: any): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/anytls/update`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('更新AnyTLS节点失败:', error)
      throw error
    }
  }

  async copyAnytlsNode(id: number): Promise<AdminApiResponse<boolean>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/server/anytls/copy`
      const response: AxiosResponse<AdminApiResponse<boolean>> = await this.instance.post(url, { id })
      return response.data
    } catch (error: any) {
      console.error('复制AnyTLS节点失败:', error)
      throw error
    }
  }

  // ==================== 系统配置管理 ====================

  // 获取系统配置
  async getConfig(key?: string): Promise<any> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/config/fetch`
      const params = key ? { key } : undefined
      const response = await this.instance.get(url, { params })
      return response.data
    } catch (error: any) {
      console.error('获取系统配置失败:', error)
      throw error
    }
  }

  // 保存系统配置
  async saveConfig(config: any): Promise<any> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/config/save`
      const response = await this.instance.post(url, config)
      return response.data
    } catch (error: any) {
      console.error('保存系统配置失败:', error)
      throw error
    }
  }

  // ==================== 自动限速管理 ====================

  // 获取自动限速配置
  async getAutoSpeedlimitConfig(): Promise<AdminApiResponse<AutoSpeedlimitConfig>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/auto-speedlimit/config`
      const response: AxiosResponse<AdminApiResponse<AutoSpeedlimitConfig>> = await this.instance.get(url)
      return response.data
    } catch (error: any) {
      console.error('获取自动限速配置失败:', error)
      throw error
    }
  }

  // 更新自动限速配置
  async updateAutoSpeedlimitConfig(config: Partial<AutoSpeedlimitConfig>): Promise<AdminApiResponse<AutoSpeedlimitConfig>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/auto-speedlimit/config`
      const response: AxiosResponse<AdminApiResponse<AutoSpeedlimitConfig>> = await this.instance.post(url, config)
      return response.data
    } catch (error: any) {
      console.error('更新自动限速配置失败:', error)
      throw error
    }
  }

  // 获取自动限速日志
  async getAutoSpeedlimitLogs(params?: {
    page?: number
    per_page?: number
    user_id?: number
    action?: 'limit' | 'restore'
  }): Promise<AdminApiResponse<{ data: AutoSpeedlimitLog[], total: number }>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/auto-speedlimit/logs`
      const response: AxiosResponse<AdminApiResponse<{ data: AutoSpeedlimitLog[], total: number }>> = await this.instance.get(url, { params })
      return response.data
    } catch (error: any) {
      console.error('获取自动限速日志失败:', error)
      throw error
    }
  }

  // 获取被限速用户列表
  async getLimitedUsers(params?: {
    page?: number
    per_page?: number
  }): Promise<AdminApiResponse<{ data: LimitedUser[], total: number }>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/auto-speedlimit/limited-users`
      const response: AxiosResponse<AdminApiResponse<{ data: LimitedUser[], total: number }>> = await this.instance.get(url, { params })
      return response.data
    } catch (error: any) {
      console.error('获取被限速用户列表失败:', error)
      throw error
    }
  }

  // 手动执行限速检查
  async executeAutoSpeedlimitCheck(): Promise<AdminApiResponse<{
    message: string
    stats: {
      checked_users: number
      limited_users: number
      restored_users: number
      total_operations: number
    }
  }>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/auto-speedlimit/execute`
      const response: AxiosResponse<AdminApiResponse<any>> = await this.instance.post(url)
      return response.data
    } catch (error: any) {
      console.error('执行自动限速检查失败:', error)
      throw error
    }
  }

  // 检查单个用户
  async checkAutoSpeedlimitUser(data: { user_id: number }): Promise<AdminApiResponse<{
    success: boolean
    message: string
    user_status: number
    speed_limit?: number
  }>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/auto-speedlimit/check-user`
      const response: AxiosResponse<AdminApiResponse<any>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('检查用户失败:', error)
      throw error
    }
  }

  // 恢复单个用户限速
  async restoreAutoSpeedlimitUser(data: { user_id: number }): Promise<AdminApiResponse<{
    success: boolean
    message: string
    speed_limit?: number
  }>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/auto-speedlimit/restore-user`
      const response: AxiosResponse<AdminApiResponse<any>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('恢复用户限速失败:', error)
      throw error
    }
  }

  // 获取自动限速统计信息
  async getAutoSpeedlimitStats(): Promise<AdminApiResponse<AutoSpeedlimitStats>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/auto-speedlimit/stats`
      const response: AxiosResponse<AdminApiResponse<AutoSpeedlimitStats>> = await this.instance.get(url)
      return response.data
    } catch (error: any) {
      console.error('获取自动限速统计失败:', error)
      throw error
    }
  }

  // 获取系统统计信息
  async getSystemStats(): Promise<AdminApiResponse<any>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/stat/getOverride`
      const response: AxiosResponse<AdminApiResponse<any>> = await this.instance.get(url)
      return response.data
    } catch (error: any) {
      console.error('获取系统统计失败:', error)
      throw error
    }
  }

  // 获取今日节点流量排行
  async getServerTodayRank(): Promise<AdminApiResponse<any>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/stat/getServerTodayRank`
      const response: AxiosResponse<AdminApiResponse<any>> = await this.instance.get(url)
      return response.data
    } catch (error: any) {
      console.error('获取今日节点排行失败:', error)
      throw error
    }
  }

  // 获取昨日节点流量排行
  async getServerLastRank(): Promise<AdminApiResponse<any>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/stat/getServerLastRank`
      const response: AxiosResponse<AdminApiResponse<any>> = await this.instance.get(url)
      return response.data
    } catch (error: any) {
      console.error('获取昨日节点排行失败:', error)
      throw error
    }
  }

  // 获取今日用户流量排行
  async getUserTodayRank(): Promise<AdminApiResponse<any>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/stat/getUserTodayRank`
      const response: AxiosResponse<AdminApiResponse<any>> = await this.instance.get(url)
      return response.data
    } catch (error: any) {
      console.error('获取今日用户排行失败:', error)
      throw error
    }
  }

  // 获取昨日用户流量排行
  async getUserLastRank(): Promise<AdminApiResponse<any>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/stat/getUserLastRank`
      const response: AxiosResponse<AdminApiResponse<any>> = await this.instance.get(url)
      return response.data
    } catch (error: any) {
      console.error('获取昨日用户排行失败:', error)
      throw error
    }
  }

  // 获取今日注册用户列表
  async getTodayRegisteredUsers(): Promise<{ data: User[], total: number }> {
    try {
      // 计算今日开始时间戳（当日0点）
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const todayStart = Math.floor(today.getTime() / 1000)

      console.log('=== 今日注册用户查询开始 ===')
      console.log('今日开始时间:', new Date(todayStart * 1000).toLocaleString())
      console.log('今日开始时间戳(秒):', todayStart)

      // 尝试获取用户列表 - 使用最简单的参数
      const result = await this.getUsers({
        current: 1,
        pageSize: 50 // 使用较小的页面大小
        // 不传入sort参数，避免可能的问题
      })

      console.log('API调用成功！获取到用户总数:', result.total, '当前页用户数:', result.data.length)

      // 调试：显示前几个用户的注册时间
      console.log('前5个用户的注册时间:')
      result.data.slice(0, 5).forEach((user, index) => {
        const userDate = new Date(user.created_at * 1000)
        console.log(`${index + 1}. ${user.email} - ${userDate.toLocaleString()} (时间戳: ${user.created_at})`)
      })

      // 过滤今日注册的用户
      const todayUsers = result.data.filter((user: User) => {
        const userTimestamp = user.created_at
        const isToday = userTimestamp >= todayStart

        if (isToday) {
          const userDate = new Date(userTimestamp * 1000)
          console.log(`✅ 今日注册用户: ${user.email} - ${userDate.toLocaleString()}`)
        }

        return isToday
      })

      console.log(`🎯 过滤结果: 找到 ${todayUsers.length} 个今日注册用户`)
      console.log('=== 今日注册用户查询结束 ===')

      return {
        data: todayUsers,
        total: todayUsers.length
      }
    } catch (error: any) {
      console.error('❌ 获取今日注册用户失败:', error)
      console.error('错误详情:', error.response?.data || error.message)
      throw error
    }
  }

  // 清理自动限速旧日志
  async cleanAutoSpeedlimitLogs(data: { keep_days: number }): Promise<AdminApiResponse<{
    message: string
    deleted_count: number
  }>> {
    try {
      const url = `/api/v1/${this.getAdminPrefix()}/auto-speedlimit/clean-logs`
      const response: AxiosResponse<AdminApiResponse<any>> = await this.instance.post(url, data)
      return response.data
    } catch (error: any) {
      console.error('清理自动限速日志失败:', error)
      throw error
    }
  }

  // ==================== 套餐管理 ====================

  // 获取套餐列表（用于套餐管理）
  async getPlansForManagement(): Promise<Plan[]> {
    try {
      const response = await this.instance.get<AdminApiResponse<Plan[]>>(
        `/api/v1/${this.getAdminPrefix()}/plan/fetch`
      )
      return response.data.data || []
    } catch (error: any) {
      console.error('获取套餐列表失败:', error)
      // 如果是网络错误或API不存在，返回空数组
      if (error.response?.status === 404 || error.code === 'NETWORK_ERROR') {
        console.warn('套餐API可能不存在，返回空数据')
        return []
      }
      return []
    }
  }

  // 获取权限组列表
  async getServerGroups(): Promise<ServerGroup[]> {
    try {
      const response = await this.instance.get<AdminApiResponse<ServerGroup[]>>(
        `/api/v1/${this.getAdminPrefix()}/server/group/fetch`
      )
      return response.data.data || []
    } catch (error: any) {
      console.error('获取权限组列表失败:', error)
      throw error
    }
  }

  // 保存权限组（新增或编辑）
  async saveServerGroup(groupData: ServerGroupRequest): Promise<ServerGroup> {
    try {
      const response = await this.instance.post<AdminApiResponse<ServerGroup>>(
        `/api/v1/${this.getAdminPrefix()}/server/group/save`,
        groupData
      )
      return response.data.data
    } catch (error: any) {
      console.error('保存权限组失败:', error)
      throw error
    }
  }

  // 删除权限组
  async deleteServerGroup(groupId: number): Promise<void> {
    try {
      await this.instance.post<AdminApiResponse<boolean>>(
        `/api/v1/${this.getAdminPrefix()}/server/group/drop`,
        { id: groupId }
      )
    } catch (error: any) {
      console.error('删除权限组失败:', error)
      throw error
    }
  }

  // 获取优惠券列表
  async getCoupons(params?: {
    current?: number
    pageSize?: number
    sort_type?: 'ASC' | 'DESC'
    sort?: string
  }): Promise<{ data: Coupon[], total: number }> {
    try {
      const response = await this.instance.get<AdminApiResponse<{ data: Coupon[], total: number }>>(
        `/api/v1/${this.getAdminPrefix()}/coupon/fetch`,
        { params }
      )

      console.log('优惠券API响应:', response.data)

      // 检查响应数据结构
      const responseData = response.data.data
      if (!responseData) {
        return { data: [], total: 0 }
      }

      // 处理不同的响应数据结构
      let coupons: Coupon[] = []
      let total = 0

      if (Array.isArray(responseData)) {
        // 如果直接是数组
        coupons = responseData
        total = responseData.length
      } else if (responseData.data && Array.isArray(responseData.data)) {
        // 如果有嵌套的data字段
        coupons = responseData.data
        total = responseData.total || responseData.data.length
      } else {
        console.warn('未知的优惠券数据结构:', responseData)
        return { data: [], total: 0 }
      }

      // 处理金额类型的优惠券值（后端存储时乘以100）和show字段
      const processedCoupons = coupons.map(coupon => ({
        ...coupon,
        value: coupon.type === 1 ? coupon.value / 100 : coupon.value,
        show: Boolean(coupon.show) // 确保show字段是布尔值
      }))

      return {
        data: processedCoupons,
        total: total
      }
    } catch (error: any) {
      console.error('获取优惠券列表失败:', error)
      throw error
    }
  }

  // 生成/保存优惠券
  async saveCoupon(couponData: CouponRequest): Promise<Coupon | string> {
    try {
      // 处理金额类型的优惠券值（发送给后端时需要乘以100）
      const processedData = {
        ...couponData,
        value: couponData.type === 1 ? couponData.value * 100 : couponData.value
      }

      const response = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/coupon/generate`,
        processedData,
        {
          responseType: couponData.generate_count ? 'blob' : 'json'
        }
      )

      // 如果是批量生成，返回CSV文件内容
      if (couponData.generate_count) {
        return response.data
      }

      // 单个优惠券创建/更新
      return response.data.data
    } catch (error: any) {
      console.error('保存优惠券失败:', error)
      throw error
    }
  }

  // 删除优惠券
  async deleteCoupon(couponId: number): Promise<void> {
    try {
      await this.instance.post<AdminApiResponse<boolean>>(
        `/api/v1/${this.getAdminPrefix()}/coupon/drop`,
        { id: couponId }
      )
    } catch (error: any) {
      console.error('删除优惠券失败:', error)
      throw error
    }
  }

  // 切换优惠券显示状态
  async toggleCouponShow(couponId: number): Promise<void> {
    try {
      await this.instance.post<AdminApiResponse<boolean>>(
        `/api/v1/${this.getAdminPrefix()}/coupon/show`,
        { id: couponId }
      )
    } catch (error: any) {
      console.error('切换优惠券状态失败:', error)
      throw error
    }
  }

  // 礼品卡管理相关方法
  async getGiftCards(params?: { pageSize?: number; current?: number; sort_type?: string; sort?: string }): Promise<{ data: GiftCard[]; total: number }> {
    try {
      console.log('获取礼品卡列表，参数:', params)

      const response = await this.instance.get<AdminApiResponse<{ data: GiftCard[], total: number }>>(
        `/api/v1/${this.getAdminPrefix()}/giftcard/fetch`,
        { params }
      )

      console.log('礼品卡API响应:', response.data)

      // 检查响应数据结构
      const responseData = response.data.data
      if (!responseData) {
        return { data: [], total: 0 }
      }

      // 处理不同的响应数据结构
      let giftcards: GiftCard[] = []
      let total = 0

      if (Array.isArray(responseData)) {
        // 如果直接是数组
        giftcards = responseData
        total = responseData.length
      } else if (responseData.data && Array.isArray(responseData.data)) {
        // 如果有嵌套的data字段
        giftcards = responseData.data
        total = responseData.total || responseData.data.length
      } else {
        console.warn('未知的礼品卡数据结构:', responseData)
        return { data: [], total: 0 }
      }

      // 处理金额类型的礼品卡值（后端存储时乘以100）
      const processedGiftCards = giftcards.map(giftcard => ({
        ...giftcard,
        value: giftcard.type === 1 ? giftcard.value / 100 : giftcard.value
      }))

      return {
        data: processedGiftCards,
        total: total
      }
    } catch (error: any) {
      console.error('获取礼品卡列表失败:', error)
      // 如果是网络错误或API不存在，返回空数据而不是抛出错误
      if (error.response?.status === 404 || error.code === 'NETWORK_ERROR') {
        console.warn('礼品卡API可能不存在，返回空数据')
        return { data: [], total: 0 }
      }
      throw error
    }
  }

  async saveGiftCard(data: GiftCardRequest): Promise<void> {
    try {
      // 处理金额类型的礼品卡值（前端显示除以100，后端存储乘以100）
      const processedData = {
        ...data,
        value: data.type === 1 ? (data.value || 0) * 100 : data.value
      }

      console.log('保存礼品卡数据:', processedData)

      const response: AxiosResponse<any> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/giftcard/generate`,
        processedData
      )

      console.log('保存礼品卡API响应:', response.data)
      console.log('保存礼品卡HTTP状态:', response.status)

      // 按照原版umi.js的逻辑：如果有code字段且不等于200，静默失败
      if (response.data && response.data.code !== undefined && response.data.code !== 200) {
        console.warn('礼品卡保存失败，code:', response.data.code, 'message:', response.data.message)
        return // 静默失败，不抛出错误
      }

      // 如果是批量生成，处理CSV下载
      if (data.generate_count && response.data && response.data.data) {
        const blob = new Blob([response.data.data], { type: 'text/plain,charset=UTF-8' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.style.display = 'none'
        link.download = `GIFTCARD ${new Date().toISOString().slice(0, 19).replace('T', ' ')}.csv`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      }
    } catch (error: any) {
      console.error('保存礼品卡失败:', error)
      // 如果是404错误，说明API不存在
      if (error.response?.status === 404) {
        throw new Error('礼品卡功能暂未开启，请联系管理员')
      }
      // 如果是网络错误
      if (error.code === 'NETWORK_ERROR' || error.code === 'ERR_NETWORK') {
        throw new Error('网络连接失败，请检查网络连接')
      }
      // 其他错误
      throw new Error(error.response?.data?.message || error.message || '保存礼品卡失败')
    }
  }

  async deleteGiftCard(id: number): Promise<void> {
    try {
      const response: AxiosResponse<any> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/giftcard/drop`,
        { id }
      )

      console.log('删除礼品卡API响应:', response.data)
      console.log('删除礼品卡HTTP状态:', response.status)

      // 按照原版umi.js的逻辑：如果有code字段且不等于200，静默失败
      if (response.data && response.data.code !== undefined && response.data.code !== 200) {
        console.warn('礼品卡删除失败，code:', response.data.code, 'message:', response.data.message)
        return // 静默失败，不抛出错误
      }

      // 删除成功
      console.log('礼品卡删除成功')
    } catch (error: any) {
      console.error('删除礼品卡失败:', error)
      // 如果是404错误，说明API不存在
      if (error.response?.status === 404) {
        throw new Error('礼品卡功能暂未开启，请联系管理员')
      }
      // 如果是网络错误
      if (error.code === 'NETWORK_ERROR' || error.code === 'ERR_NETWORK') {
        throw new Error('网络连接失败，请检查网络连接')
      }
      // 其他错误
      throw new Error(error.response?.data?.message || error.message || '删除礼品卡失败')
    }
  }

  // 创建套餐
  async createPlan(data: CreatePlanRequest): Promise<Plan> {
    try {
      const response = await this.instance.post<AdminApiResponse<Plan>>(
        `/api/v1/${this.getAdminPrefix()}/plan/save`,
        data
      )
      return response.data.data
    } catch (error: any) {
      console.error('创建套餐失败:', error)
      throw error
    }
  }

  // 更新套餐
  async updatePlan(data: UpdatePlanRequest): Promise<Plan> {
    try {
      const response = await this.instance.post<AdminApiResponse<Plan>>(
        `/api/v1/${this.getAdminPrefix()}/plan/save`,
        data
      )
      return response.data.data
    } catch (error: any) {
      console.error('更新套餐失败:', error)
      throw error
    }
  }

  // 删除套餐
  async deletePlan(planId: number): Promise<void> {
    try {
      const response = await this.instance.post<AdminApiResponse<boolean>>(
        `/api/v1/${this.getAdminPrefix()}/plan/drop`,
        { id: planId }
      )
      if (!response.data.data) {
        throw new Error('删除失败')
      }
    } catch (error: any) {
      console.error('删除套餐失败:', error)
      throw error
    }
  }

  // 更新套餐单个字段（如销售状态）
  async updatePlanField(planId: number, field: string, value: any): Promise<void> {
    try {
      const response = await this.instance.post<{ data: boolean }>(
        `/api/v1/${this.getAdminPrefix()}/plan/update`,
        {
          id: planId,
          [field]: value
        }
      )

      // 这个接口返回 {data: true}，没有code字段
      if (response.status === 200 && response.data.data === true) {
        console.log('更新成功')
      } else {
        throw new Error('更新失败')
      }
    } catch (error: any) {
      console.error('更新套餐字段失败:', error)
      throw error
    }
  }

  // 用户管理相关API
  async getUsers(params?: {
    pageSize?: number
    current?: number
    sort_type?: string
    sort?: string
    filter?: UserFilter[]
  }): Promise<{ data: User[]; total: number }> {
    try {
      // 按照原版umi.js的格式构建请求体
      const requestBody: any = {
        filter: params?.filter || [],
        // pagination对象
        current: params?.current || 1,
        pageSize: params?.pageSize || 10
      }

      // sort对象 - 只有在有排序时才添加（参数名与原版保持一致）
      if (params?.sort_type && params?.sort) {
        requestBody.sort_type = params.sort_type  // ASC/DESC
        requestBody.sort = params.sort            // 字段名
      }

      console.log('=== 用户列表API调用详情 ===')
      console.log('请求URL:', `/api/v1/${this.getAdminPrefix()}/user/fetch`)
      console.log('请求方法: GET')
      console.log('请求参数:', JSON.stringify(requestBody, null, 2))
      console.log('过滤器详情:', params?.filter)

      const response: AxiosResponse<any> = await this.instance.get(
        `/api/v1/${this.getAdminPrefix()}/user/fetch`,
        {
          params: requestBody,
          timeout: 30000 // 30秒超时
        }
      )

      console.log('获取用户列表API响应:', response.data)

      if (response.data && response.data.code !== undefined && response.data.code !== 200) {
        console.warn('获取用户列表失败，code:', response.data.code, 'message:', response.data.message)
        return { data: [], total: 0 }
      }

      const users = response.data?.data || []
      const total = response.data?.total || 0

      return { data: users, total }
    } catch (error: any) {
      console.error('获取用户列表失败:', error)
      if (error.response?.status === 404) {
        return { data: [], total: 0 }
      }
      throw new Error(error.response?.data?.message || error.message || '获取用户列表失败')
    }
  }

  async saveUser(data: UserRequest): Promise<void> {
    try {
      const response: AxiosResponse<any> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/user/update`,
        data
      )

      console.log('保存用户API响应:', response.data)

      // 按照原版umi.js的逻辑：如果有code字段且不等于200，静默失败
      if (response.data && response.data.code !== undefined && response.data.code !== 200) {
        console.warn('用户保存失败，code:', response.data.code, 'message:', response.data.message)
        return // 静默失败，不抛出错误
      }
    } catch (error: any) {
      console.error('保存用户失败:', error)
      if (error.response?.status === 404) {
        console.warn('用户保存API不存在')
        return
      }
      // 其他错误
      throw new Error(error.response?.data?.message || error.message || '保存用户失败')
    }
  }

  // 根据原版V2Board逻辑，创建用户使用generate方法
  async generateUser(data: {
    email_prefix?: string
    email_suffix?: string
    email?: string
    password?: string
    expired_at?: number
    plan_id?: number
    generate_count?: number
  }): Promise<void> {
    try {
      // 如果传入完整邮箱，拆分为前缀和后缀
      let requestData = { ...data }
      if (data.email && !data.email_prefix && !data.email_suffix) {
        const emailParts = data.email.split('@')
        if (emailParts.length === 2) {
          requestData.email_prefix = emailParts[0]
          requestData.email_suffix = emailParts[1]
          delete requestData.email
        }
      }

      const response: AxiosResponse<any> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/user/generate`,
        requestData
      )

      console.log('创建用户API响应:', response.data)

      if (response.data && response.data.code !== undefined && response.data.code !== 200) {
        throw new Error(response.data.message || '创建用户失败')
      }
    } catch (error: any) {
      console.error('创建用户失败:', error)
      throw new Error(error.response?.data?.message || error.message || '创建用户失败')
    }
  }



  async deleteUser(id: number): Promise<void> {
    try {
      const response: AxiosResponse<any> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/user/delUser`,
        { id }
      )

      console.log('删除用户API响应:', response.data)

      if (response.data && response.data.code !== undefined && response.data.code !== 200) {
        console.warn('用户删除失败，code:', response.data.code, 'message:', response.data.message)
        return
      }
    } catch (error: any) {
      console.error('删除用户失败:', error)
      if (error.response?.status === 404) {
        console.warn('用户删除API不存在')
        return
      }
      throw new Error(error.response?.data?.message || error.message || '删除用户失败')
    }
  }

  async banUsers(filter: UserFilter[]): Promise<void> {
    try {
      const response: AxiosResponse<any> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/user/ban`,
        { filter }
      )

      console.log('封禁用户API响应:', response.data)

      if (response.data && response.data.code !== undefined && response.data.code !== 200) {
        console.warn('用户封禁失败，code:', response.data.code, 'message:', response.data.message)
        return
      }
    } catch (error: any) {
      console.error('封禁用户失败:', error)
      if (error.response?.status === 404) {
        console.warn('用户封禁API不存在')
        return
      }
      throw new Error(error.response?.data?.message || error.message || '封禁用户失败')
    }
  }

  async deleteAllUsers(filter: UserFilter[]): Promise<void> {
    try {
      const response: AxiosResponse<any> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/user/allDel`,
        { filter }
      )

      console.log('批量删除用户API响应:', response.data)

      if (response.data && response.data.code !== undefined && response.data.code !== 200) {
        console.warn('批量删除用户失败，code:', response.data.code, 'message:', response.data.message)
        return
      }
    } catch (error: any) {
      console.error('批量删除用户失败:', error)
      if (error.response?.status === 404) {
        console.warn('批量删除用户API不存在')
        return
      }
      throw new Error(error.response?.data?.message || error.message || '批量删除用户失败')
    }
  }

  async resetUserSecret(id: number): Promise<void> {
    try {
      const response: AxiosResponse<any> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/user/resetSecret`,
        { id }
      )

      console.log('重置用户密钥API响应:', response.data)

      if (response.data && response.data.code !== undefined && response.data.code !== 200) {
        console.warn('重置用户密钥失败，code:', response.data.code, 'message:', response.data.message)
        return
      }
    } catch (error: any) {
      console.error('重置用户密钥失败:', error)
      if (error.response?.status === 404) {
        console.warn('重置用户密钥API不存在')
        return
      }
      throw new Error(error.response?.data?.message || error.message || '重置用户密钥失败')
    }
  }

  async exportUsersCSV(filter: UserFilter[]): Promise<void> {
    try {
      const response: AxiosResponse<any> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/user/dumpCSV`,
        { filter },
        { responseType: 'blob' }
      )

      // 创建下载链接
      const blob = new Blob([response.data], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `users_${new Date().getTime()}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error: any) {
      console.error('导出用户CSV失败:', error)
      throw new Error(error.response?.data?.message || error.message || '导出用户CSV失败')
    }
  }

  async sendMailToUsers(filter: UserFilter[], subject: string, content: string): Promise<void> {
    try {
      const response: AxiosResponse<any> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/user/sendMail`,
        { filter, subject, content }
      )

      console.log('发送邮件API响应:', response.data)

      if (response.data && response.data.code !== undefined && response.data.code !== 200) {
        console.warn('发送邮件失败，code:', response.data.code, 'message:', response.data.message)
        return
      }
    } catch (error: any) {
      console.error('发送邮件失败:', error)
      if (error.response?.status === 404) {
        console.warn('发送邮件API不存在')
        return
      }
      throw new Error(error.response?.data?.message || error.message || '发送邮件失败')
    }
  }

  // ==================== 公告管理相关方法 ====================

  /**
   * 获取公告列表
   */
  async fetchNotices(): Promise<NoticeListResponse> {
    try {
      const response: AxiosResponse<NoticeListResponse> = await this.instance.get(
        `/api/v1/${this.getAdminPrefix()}/notice/fetch`
      )
      return response.data
    } catch (error: any) {
      console.error('获取公告列表失败:', error)
      throw new Error(error.response?.data?.message || error.message || '获取公告列表失败')
    }
  }

  /**
   * 保存公告（新建/编辑）
   */
  async saveNotice(notice: Notice): Promise<NoticeOperationResponse> {
    try {
      // 构建符合后端验证规则的数据
      const data: any = {
        title: notice.title,
        content: notice.content,
        img_url: notice.img_url || null,
        tags: notice.tags || null
      }

      // 如果是编辑，添加id
      if (notice.id) {
        data.id = notice.id
      }

      const response: AxiosResponse<NoticeOperationResponse> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/notice/save`,
        data
      )
      return response.data
    } catch (error: any) {
      console.error('保存公告失败:', error)
      throw new Error(error.response?.data?.message || error.message || '保存公告失败')
    }
  }

  /**
   * 删除公告
   */
  async deleteNotice(id: number): Promise<NoticeOperationResponse> {
    try {
      const response: AxiosResponse<NoticeOperationResponse> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/notice/drop`,
        { id }
      )
      return response.data
    } catch (error: any) {
      console.error('删除公告失败:', error)
      throw new Error(error.response?.data?.message || error.message || '删除公告失败')
    }
  }

  /**
   * 切换公告显示状态
   */
  async toggleNoticeShow(id: number): Promise<NoticeOperationResponse> {
    try {
      const response: AxiosResponse<NoticeOperationResponse> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/notice/show`,
        { id }
      )
      return response.data
    } catch (error: any) {
      console.error('切换公告显示状态失败:', error)
      throw new Error(error.response?.data?.message || error.message || '切换公告显示状态失败')
    }
  }

  // ==================== 工单管理相关方法 ====================

  /**
   * 获取工单列表
   */
  async fetchTickets(params?: {
    current?: number
    pageSize?: number
    status?: number
    reply_status?: number[]
    email?: string
  }): Promise<TicketListResponse> {
    try {
      const response: AxiosResponse<TicketListResponse> = await this.instance.get(
        `/api/v1/${this.getAdminPrefix()}/ticket/fetch`,
        { params }
      )
      return response.data
    } catch (error: any) {
      console.error('获取工单列表失败:', error)
      throw new Error(error.response?.data?.message || error.message || '获取工单列表失败')
    }
  }

  /**
   * 获取工单详情
   */
  async fetchTicketById(id: number): Promise<TicketDetailResponse> {
    try {
      const response: AxiosResponse<TicketDetailResponse> = await this.instance.get(
        `/api/v1/${this.getAdminPrefix()}/ticket/fetch`,
        { params: { id } }
      )
      return response.data
    } catch (error: any) {
      console.error('获取工单详情失败:', error)
      throw new Error(error.response?.data?.message || error.message || '获取工单详情失败')
    }
  }

  /**
   * 回复工单
   */
  async replyTicket(id: number, message: string): Promise<TicketOperationResponse> {
    try {
      const response: AxiosResponse<TicketOperationResponse> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/ticket/reply`,
        { id, message }
      )
      return response.data
    } catch (error: any) {
      console.error('回复工单失败:', error)
      throw new Error(error.response?.data?.message || error.message || '回复工单失败')
    }
  }

  /**
   * 关闭工单
   */
  async closeTicket(id: number): Promise<TicketOperationResponse> {
    try {
      const response: AxiosResponse<TicketOperationResponse> = await this.instance.post(
        `/api/v1/${this.getAdminPrefix()}/ticket/close`,
        { id }
      )
      return response.data
    } catch (error: any) {
      console.error('关闭工单失败:', error)
      throw new Error(error.response?.data?.message || error.message || '关闭工单失败')
    }
  }

}

// 导出单例实例
export const adminApiClient = new AdminApiClient()

// 自动限速API简化导出
export const autoSpeedlimitApi = {
  getConfig: () => adminApiClient.getAutoSpeedlimitConfig(),
  updateConfig: (config: Partial<AutoSpeedlimitConfig>) => adminApiClient.updateAutoSpeedlimitConfig(config),
  getLogs: (params?: any) => adminApiClient.getAutoSpeedlimitLogs(params),
  getLimitedUsers: (params?: any) => adminApiClient.getLimitedUsers(params),
  executeCheck: () => adminApiClient.executeAutoSpeedlimitCheck(),
  checkUser: (data: { user_id: number }) => adminApiClient.checkAutoSpeedlimitUser(data),
  restoreUser: (data: { user_id: number }) => adminApiClient.restoreAutoSpeedlimitUser(data),
  getStats: () => adminApiClient.getAutoSpeedlimitStats(),
  cleanLogs: (data: { keep_days: number }) => adminApiClient.cleanAutoSpeedlimitLogs(data),
}

// 公告管理API简化导出
export const noticeApi = {
  fetchNotices: () => adminApiClient.fetchNotices(),
  saveNotice: (notice: Notice) => adminApiClient.saveNotice(notice),
  deleteNotice: (id: number) => adminApiClient.deleteNotice(id),
  toggleNoticeShow: (id: number) => adminApiClient.toggleNoticeShow(id),
}

// ==================== 工单管理API ====================

export const ticketApi = {
  fetchTickets: (params?: {
    current?: number
    pageSize?: number
    status?: number
    reply_status?: number[]
    email?: string
  }) => adminApiClient.fetchTickets(params),
  fetchTicketById: (id: number) => adminApiClient.fetchTicketById(id),
  replyTicket: (id: number, message: string) => adminApiClient.replyTicket(id, message),
  closeTicket: (id: number) => adminApiClient.closeTicket(id),
}
